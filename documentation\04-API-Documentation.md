# API Documentation
## LumusAI - Intelligent Document Processing Service

**API Version:** 1.0  
**Date:** December 2024  
**Base URL:** `http://localhost:8000` (development)  
**Project:** LumusAI  

---

## Table of Contents

1. [Overview](#1-overview)
2. [Authentication](#2-authentication)
3. [Endpoints](#3-endpoints)
4. [Request/Response Formats](#4-requestresponse-formats)
5. [<PERSON>rro<PERSON> Handling](#5-error-handling)
6. [Code Examples](#6-code-examples)
7. [Rate Limits](#7-rate-limits)
8. [SDKs and Libraries](#8-sdks-and-libraries)

---

## 1. Overview

### 1.1 Introduction
LumusAI provides a RESTful API for intelligent document processing. The service can extract structured information from CVs/resumes, legal documents, and invoices using advanced AI models.

### 1.2 API Features
- **Multi-format Support:** PDF, DOCX, Excel, and image files
- **Structured Output:** JSON responses with validated data models
- **Concurrent Processing:** Multiple documents processed simultaneously
- **Real-time Monitoring:** Health checks and task status
- **Error Recovery:** Automatic retries and comprehensive error handling

### 1.3 Supported Document Types
- **CVs/Resumes:** Personal information, work experience, education, skills
- **Legal Documents:** Colombian Tutela documents (contestación, fallo, desacato, correo)
- **Invoices:** Purchase invoices, utility bills, delivery tickets, credit notes

## 2. Authentication

### 2.1 Current Authentication
The current version of LumusAI does not require authentication for API access. All endpoints are publicly accessible.

### 2.2 Future Authentication (Planned)
Future versions will support:
- API key authentication
- JWT token-based authentication
- Role-based access control

### 2.3 External Service Authentication
LumusAI requires valid OpenAI/Azure OpenAI API credentials configured via environment variables.

## 3. Endpoints

### 3.1 Document Processing

#### POST /process
Process a document and extract structured information.

**Parameters:**
- `action` (string, required): Document type identifier
- `file` (file, optional): Document file upload
- `data` (string, optional): URL or text data

**Supported Actions:**
- `cv` - CV/Resume processing
- `invoice` - Invoice processing
- `tutela_contestacion` - Legal contestation documents
- `tutela_fallo` - Legal ruling documents
- `tutela_desacato` - Legal contempt documents
- `tutela_correo` - Legal email communications

**Request Example:**
```http
POST /process
Content-Type: multipart/form-data

action=cv
file=@resume.pdf
```

**Response Example:**
```json
{
  "status": "success",
  "data": {
    "personal_info": {
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "******-0123",
      "location": "New York, NY"
    },
    "work_experience": [
      {
        "company": "Tech Corp",
        "position": "Software Engineer",
        "start_date": "2020-01",
        "end_date": "2023-12",
        "duration_months": 48,
        "responsibilities": [
          "Developed web applications",
          "Led team of 3 developers"
        ]
      }
    ],
    "skills": [
      {
        "name": "Python",
        "category": "Programming",
        "experience_months": 60
      }
    ]
  },
  "metadata": {
    "processing_time": 45.2,
    "token_usage": {
      "prompt_tokens": 1500,
      "completion_tokens": 800,
      "total_tokens": 2300,
      "cost": 0.046
    }
  }
}
```

### 3.2 Health Monitoring

#### GET /health
Get system health status and current task information.

**Response Example:**
```json
{
  "status": "ok",
  "version": "4.9.25.12.12",
  "message": "Service is running",
  "system_metrics": {
    "cpu_usage_percent": 25.4,
    "memory_usage_percent": 68.2
  },
  "tasks": {
    "processing_count": 2,
    "processing_details": [
      {
        "task_id": "task_123",
        "action": "cv",
        "running_time_seconds": 12.5,
        "file_name": "resume.pdf"
      }
    ],
    "waiting_count": 1
  }
}
```

### 3.3 Maintenance

#### GET /maintenance/tasks
Perform task maintenance and cleanup operations.

**Response Example:**
```json
{
  "status": "success",
  "message": "Task maintenance completed",
  "cleaned_tasks": 3,
  "active_tasks": 2
}
```

## 4. Request/Response Formats

### 4.1 Request Format

**Content Type:** `multipart/form-data`

**Required Fields:**
- `action`: Document type identifier

**Optional Fields:**
- `file`: Document file (PDF, DOCX, Excel, image)
- `data`: URL or text content

**File Size Limits:**
- Maximum file size: 50MB
- Supported formats: PDF, DOCX, XLS, XLSX, PNG, JPG, JPEG, TXT

### 4.2 Response Format

**Success Response:**
```json
{
  "status": "success",
  "data": {
    // Structured data based on document type
  },
  "metadata": {
    "processing_time": 45.2,
    "token_usage": {
      "prompt_tokens": 1500,
      "completion_tokens": 800,
      "total_tokens": 2300,
      "cost": 0.046
    }
  }
}
```

**Error Response:**
```json
{
  "status": "error",
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid document format",
    "details": "Supported formats: PDF, DOCX, XLS, XLSX"
  },
  "timestamp": "2024-12-01T10:30:00Z"
}
```

### 4.3 Data Models

#### CV Response Model
```json
{
  "personal_info": {
    "name": "string",
    "email": "string",
    "phone": "string",
    "location": "string",
    "linkedin": "string"
  },
  "summary": "string",
  "education": [
    {
      "institution": "string",
      "degree": "string",
      "field_of_study": "string",
      "graduation_date": "string"
    }
  ],
  "work_experience": [
    {
      "company": "string",
      "position": "string",
      "start_date": "string",
      "end_date": "string",
      "duration_months": 0,
      "responsibilities": ["string"],
      "technologies": ["string"]
    }
  ],
  "skills": [
    {
      "name": "string",
      "category": "string",
      "experience_months": 0
    }
  ],
  "extraction_cost": {
    "tokens": {
      "prompt_tokens": 7676,
      "completion_tokens": 484,
      "total_tokens": 8160
    },
    "cost_usd": 0.024030000000000003
  }
}
```

#### Invoice Response Model
```json
{
  "invoice_number": "string",
  "invoice_type": "string",
  "description": "string",
  "invoice_date": "string",
  "due_date": "string",
  "total_amount": {
    "subtotal": 0.0,
    "tax_amount": 0.0,
    "total": 0.0,
    "currency": "string"
  },
  "invoice_items": [
    {
      "description": "string",
      "quantity": 0.0,
      "unit_price": 0.0,
      "total_price": 0.0
    }
  ]
}
```

## 5. Error Handling

### 5.1 HTTP Status Codes

| Code | Description | Usage |
|------|-------------|-------|
| 200 | OK | Successful request |
| 400 | Bad Request | Invalid request parameters |
| 422 | Unprocessable Entity | Data validation errors |
| 500 | Internal Server Error | Server-side errors |
| 503 | Service Unavailable | System overloaded |

### 5.2 Error Response Format

```json
{
  "status": "error",
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": "Additional error details"
  },
  "timestamp": "2024-12-01T10:30:00Z"
}
```

### 5.3 Common Error Codes

| Code | Description | Resolution |
|------|-------------|------------|
| `VALIDATION_ERROR` | Invalid input parameters | Check request format and parameters |
| `UNSUPPORTED_FORMAT` | Unsupported file format | Use supported file formats |
| `FILE_TOO_LARGE` | File exceeds size limit | Reduce file size or split document |
| `PROCESSING_ERROR` | Document processing failed | Retry with different document |
| `AI_SERVICE_ERROR` | AI model service error | Retry request or check service status |
| `RATE_LIMIT_EXCEEDED` | Too many requests | Reduce request frequency |

## 6. Code Examples

### 6.1 Python Example

```python
import requests

# Process a CV
url = "http://localhost:8000/process"
files = {"file": open("resume.pdf", "rb")}
data = {"action": "cv"}

response = requests.post(url, files=files, data=data)
result = response.json()

if result["status"] == "success":
    cv_data = result["data"]
    print(f"Name: {cv_data['personal_info']['name']}")
    print(f"Email: {cv_data['personal_info']['email']}")
else:
    print(f"Error: {result['error']['message']}")
```

### 6.2 cURL Example

```bash
# Process a CV
curl -X POST "http://localhost:8000/process" \
     -F "action=cv" \
     -F "file=@resume.pdf"

# Process an invoice
curl -X POST "http://localhost:8000/process" \
     -F "action=invoice" \
     -F "file=@invoice.pdf"

# Check system health
curl -X GET "http://localhost:8000/health"
```

### 6.3 JavaScript Example

```javascript
// Process a document
const formData = new FormData();
formData.append('action', 'cv');
formData.append('file', fileInput.files[0]);

fetch('http://localhost:8000/process', {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(data => {
    if (data.status === 'success') {
        console.log('CV processed:', data.data);
    } else {
        console.error('Error:', data.error.message);
    }
})
.catch(error => console.error('Request failed:', error));
```

### 6.4 URL-based Processing

```bash
# Process document from URL
curl -X POST "http://localhost:8000/process" \
     -F "action=cv" \
     -F "data=https://example.com/resume.pdf"
```

## 7. Rate Limits

### 7.1 Current Limits
- **Concurrent Processing:** 4 simultaneous tasks (configurable)
- **Queue Size:** Unlimited (limited by system resources)
- **File Size:** 50MB maximum per file

### 7.2 Rate Limiting Headers
Future versions will include rate limiting headers:
- `X-RateLimit-Limit`: Request limit per time window
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Time when rate limit resets

### 7.3 Handling Rate Limits
When rate limits are exceeded:
- HTTP 503 status code returned
- Retry after specified time
- Implement exponential backoff in client code

## 8. SDKs and Libraries

### 8.1 Official SDKs
Currently, no official SDKs are available. The API can be accessed using standard HTTP libraries.

### 8.2 Recommended Libraries

**Python:**
- `requests` - HTTP client library
- `httpx` - Async HTTP client
- `aiohttp` - Async HTTP client/server

**JavaScript/Node.js:**
- `fetch` - Built-in HTTP client
- `axios` - Promise-based HTTP client
- `node-fetch` - Node.js fetch implementation

**Other Languages:**
- **Java:** OkHttp, Apache HttpClient
- **C#:** HttpClient, RestSharp
- **Go:** net/http package
- **PHP:** Guzzle, cURL

### 8.3 Integration Examples

**Async Python Example:**
```python
import aiohttp
import asyncio

async def process_document(file_path, action):
    async with aiohttp.ClientSession() as session:
        data = aiohttp.FormData()
        data.add_field('action', action)
        data.add_field('file', open(file_path, 'rb'))
        
        async with session.post('http://localhost:8000/process', data=data) as response:
            return await response.json()

# Usage
result = asyncio.run(process_document('resume.pdf', 'cv'))
```

---

**Document Control:**
- **Version:** 1.0
- **Status:** Final
- **Last Updated:** December 2024
- **Next Review:** Upon API changes
