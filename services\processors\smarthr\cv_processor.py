from services.processors.base_processor import DocumentProcessor
from utils.openai_client import OpenAIClient
from utils.langchain_client import LangChainClient
from fastapi import UploadFile, HTTPException
from typing import Optional, List
from pydantic import BaseModel
import os
import tempfile
import fitz
import base64
import requests
import json
from domain.models.smarthr.cv_model import CV
import asyncio  # Used for CancelledError handling
from utils.helpers import get_text_from_pdf, get_text_from_word, get_text_from_word_by_pages


def merge_cv_data(existing_data: dict, new_data: dict) -> dict:
    """Merge new CV data into existing data, preserving existing values when new values are null.

    Args:
        existing_data: The accumulated CV data from previous pages
        new_data: The new CV data extracted from the current page

    Returns:
        The merged CV data
    """
    if not existing_data:
        return new_data
    if not new_data:
        return existing_data

    result = existing_data.copy()

    # Process each top-level field
    for key, new_value in new_data.items():
        # Skip if the new value is None/null
        if new_value is None:
            continue

        # If the field doesn't exist in the result yet, add it
        if key not in result or result[key] is None:
            result[key] = new_value
            continue

        # Handle different types of fields
        if isinstance(new_value, dict) and isinstance(result[key], dict):
            # Recursively merge nested dictionaries (like personal_info)
            result[key] = merge_cv_data(result[key], new_value)

        elif isinstance(new_value, list) and isinstance(result[key], list):
            # For lists (like work_experience, skills, etc.), append new items
            # We need to be careful not to duplicate items

            # If the list contains dictionaries (like work_experience)
            if new_value and isinstance(new_value[0], dict):
                # Special handling for work_experience to merge responsibilities
                if key == 'work_experience':
                    # Create a map of existing work experiences by job title and company name
                    existing_work_exp_map = {}
                    for i, item in enumerate(result[key]):
                        if 'job_title' in item and 'company_name' in item:
                            job_key = f"{item['job_title']}_{item['company_name']}"
                            existing_work_exp_map[job_key] = i

                    # Process each new work experience
                    for new_item in new_value:
                        if 'job_title' in new_item and 'company_name' in new_item:
                            job_key = f"{new_item['job_title']}_{new_item['company_name']}"

                            # If this job already exists, merge the responsibilities
                            if job_key in existing_work_exp_map:
                                existing_index = existing_work_exp_map[job_key]
                                existing_item = result[key][existing_index]

                                # Merge responsibilities if they exist in both
                                if 'responsibilities' in new_item and new_item['responsibilities'] and 'responsibilities' in existing_item:
                                    # Add only new responsibilities that don't already exist
                                    existing_resp_set = set(existing_item['responsibilities'])
                                    for resp in new_item['responsibilities']:
                                        if resp not in existing_resp_set:
                                            existing_item['responsibilities'].append(resp)
                                            existing_resp_set.add(resp)
                                # If responsibilities only exist in the new item, add them
                                elif 'responsibilities' in new_item and new_item['responsibilities']:
                                    existing_item['responsibilities'] = new_item['responsibilities']

                                # Merge skills if they exist in both
                                if 'skills' in new_item and new_item['skills'] and 'skills' in existing_item:
                                    existing_skill_names = {skill['name'] for skill in existing_item['skills'] if 'name' in skill}
                                    for skill in new_item['skills']:
                                        if 'name' in skill and skill['name'] not in existing_skill_names:
                                            existing_item['skills'].append(skill)
                                            existing_skill_names.add(skill['name'])
                                # If skills only exist in the new item, add them
                                elif 'skills' in new_item and new_item['skills']:
                                    existing_item['skills'] = new_item['skills']
                            else:
                                # This is a new job, add it to the result
                                result[key].append(new_item)
                                # Update the map with the new job
                                existing_work_exp_map[job_key] = len(result[key]) - 1
                        else:
                            # If job_title or company_name is missing, just add it
                            result[key].append(new_item)
                else:
                    # For other types of lists with dictionaries (like skills, education)
                    # Create a set of existing items for comparison
                    existing_items = set()
                    for item in result[key]:
                        if 'name' in item:
                            existing_items.add(item['name'])
                        elif 'institution_name' in item:
                            existing_items.add(item['institution_name'])
                        else:
                            existing_items.add(str(item))

                    # Add new items that don't exist yet
                    for item in new_value:
                        if 'name' in item and item['name'] in existing_items:
                            continue
                        elif 'institution_name' in item and item['institution_name'] in existing_items:
                            continue
                        elif str(item) in existing_items:
                            continue
                        result[key].append(item)
            else:
                # For simple lists (like strings), just add new items
                for item in new_value:
                    if item not in result[key]:
                        result[key].append(item)

        # For other types, prefer non-null values
        elif new_value is not None:
            result[key] = new_value

    return result


def cleanup_file(file_path: str, temp_files_list: Optional[List[str]] = None) -> bool:
    """Safely remove a file and optionally remove it from tracking list.

    Args:
        file_path: Path to the file to remove
        temp_files_list: Optional list of tracked files to update

    Returns:
        True if the file was successfully removed, False otherwise
    """
    if not file_path:
        return False

    if temp_files_list is not None and file_path in temp_files_list:
        temp_files_list.remove(file_path)

    if os.path.exists(file_path):
        try:
            os.remove(file_path)
            return True
        except Exception as e:
            print(f"Warning: Failed to remove {file_path}: {e}")
    return False


class TempFileTracker:
    """Context manager for tracking and cleaning up temporary files.

    Ensures that all tracked files are properly cleaned up when the context exits,
    even if an exception occurs.
    """
    def __init__(self):
        self.files: List[str] = []

    def add(self, file_path: str) -> str:
        """Add a file to be tracked for cleanup.

        Args:
            file_path: Path to the file to track

        Returns:
            The same file path for convenience
        """
        self.files.append(file_path)
        return file_path

    def remove(self, file_path: str) -> bool:
        """Remove a file from tracking and delete it if it exists.

        Args:
            file_path: Path to the file to remove

        Returns:
            True if the file was successfully removed, False otherwise
        """
        return cleanup_file(file_path, self.files)

    def __enter__(self):
        return self

    def __exit__(self, *_):
        """Clean up all tracked files when exiting the context.

        The underscore parameter name indicates that we're deliberately ignoring the arguments.
        """
        for file_path in list(self.files):  # Create a copy of the list to avoid modification during iteration
            cleanup_file(file_path, self.files)

prompt = """
                Extract ALL relevant information from the provided CV and present it in the following standardized format,
                without adding any notes or additional information:

                If the information is missing DO NOT make up new info, leave it blank.

                **Personal Information:**
                - Full Name: [Value]
                - Country: [Value]
                - City: [Value]
                - Address: [Value]
                - Phone Number: [Value]
                - Email: [Value] (If multiple emails are found, separate them with semicolons)
                - LinkedIn Profile: [Value]
                - Website: [Value]

                **Summary:**
                [Value]

                **Education:**
                1. Institution Name: [Value]
                - Degree: [Value]
                - Field of Study: [Value]
                - Start Date: [Value]
                - End Date: [Value]
                - Location: [Value]
                - Description: [Value]
                (Continue numbering for each educational experience.)

                **Work Experience:**
                1. Job Title: [Value]
                - Company Name: [Value]
                - Start Date: [Value]
                - End Date: [Value]
                - Location: [Value]
                - Responsibilities:
                    - [Responsibility 1]
                    - [Responsibility 2]
                    - (Continue listing responsibilities.)
                - Technical skills used:
                    - [Technology 1]
                    - Months of Experience: [Value]
                    - [Technology 2]
                    - Months of Experience: [Value]
                    - (Continue listing Technical skills.)
                (Continue numbering for each work experience. Without omitting any work experience.)

                **Skills:**
                1. Skill type: [Value]
                    1. Skill name: [Value]
                    - Proficiency Level: [Value]
                    - Years of Experience: [Value]
                    (Continue numbering for each skill name.)
                (Continue numbering for each skill type.)

                **Soft Skills:**
                1. Skill type: [Value]
                    - Skill name: [Value]
                    - Description: [Value]
                    (Continue numbering for each soft skill.)

                **Certifications:**
                1. Name: [Value]
                - Issuing Organization: [Value]
                - Issue Date: [Value]
                - Expiration Date: [Value]
                - Credential ID: [Value]
                - Credential URL: [Value]
                (Continue numbering for each certification.)

                **Languages:**
                1. Language: [Value]
                - Proficiency Level: [Value]
                (Continue numbering for each language.)

                **Projects:**
                1. Name: [Value]
                - Description: [Value]
                - Role: [Value]
                - Technical skills used:
                    - [Technology 1]
                        - Months of Experience: [Value]
                    - [Technology 2]
                        - Months of Experience: [Value]
                    - (Continue listing technologies.)
                - Start Date: [Value]
                - End Date: [Value]
                - URL: [Value]
                (Continue numbering for each project.)

                **Roles:**
                (List the top 3 most relevant positions or roles that the candidate can perform based on their experience, order them in order from most relevant to least)
                - Role 1: [Value]
                - Role 2: [Value]
                - Role 3: [Value]

                **References:**
                1. Name: [Value]
                - Relationship: [Value]
                - Phone Number: [Value]
                - Email: [Value] (If multiple emails are found, separate them with semicolons)
                (Continue numbering for each reference.)

                **Hobbies and Interests:**
                - [Hobby or Interest 1]
                - [Hobby or Interest 2]
                (Continue listing hobbies and interests.)

                **Instructions:**
                + Extract ALL relevant information from the CV, including personal details, education, work experience, skills, certifications, languages, projects, references, and hobbies/interests.
                + IMPORTANT: Include ALL work experiences found in the CV. Do not omit or summarize any work experience entries.
                + Include in **Work Experience:** jobs as teacher or researcher in universities.
                + If **Education:** items present just one year, repeat the same year in "Start date" and in the "End date".
                + DO NOT CONFUSE THE "SKILL TYPE" WITH THE "SKILL".  "SKILL TYPE" IS THE GENERAL CLASSIFICATION OF THE SKILL.
                + For the best calculus of the months of experience, "Today", "Present" or "Actual" makes reference of the today's date.
                + IMPORTANT: If multiple email addresses are found in the CV, extract ALL of them and separate them with semicolons (;). For example: "<EMAIL>;<EMAIL>".
                + For each **Work Experience:** in the document, DEDUCE the "Technical skills used" found in the description of EACH project and DEDUCE their "Months of Experience" according to the time extension of the project.
                + Separate each individual skill.
                + **Skills:** section must be extracted from the skills found in "Technical skills used" in **Work Experience:** section.
                + SUM the months extracted for EACH skill found in EACH **Work Experience:**, sum and display for each skill in **Skills:**.  Display the amount in years.
                + IF you find other technical skills with "Years of Experience", INCLUDE THESE SKILLS at the end of the **Skills:** section.
                + IF you find other technical skills without "Years of Experience", INCLUDE THESE SKILLS at the end of the **Skills:** section and declare in "Years of Experience" as "NOT DEFINED".
                + Include every section and sub-section as shown above, even if some sections are empty.
                + Do not add any notes or additional comments; only include the information extracted from the CV.
                + Present all information clearly and in the order shown above.
                + For lists (like responsibilities and technologies used), list each item on a separate line with a bullet point.
                + Use the provided format exactly, including the labels and the order.
                + DO NOT use phrases like "Other work experiences omitted for brevity" or similar - include ALL experiences.

                TRANSLATE ALL THE INFORMATION EXTRACTED FROM THE TEXT TO ENGLISH.
                """

class CVProcessor(DocumentProcessor):
    """
    Processor for extracting structured information from CV/resume documents.

    Supports multiple file formats including PDF and DOCX files. Processes CVs to extract
    structured information about candidates' professional profiles using language models.

    Key Features:
    - Comprehensive information extraction including:
        - Personal and contact details
        - Professional summary
        - Education history
        - Work experience with technical skills used
        - Skills assessment with experience calculation
        - Certifications and languages
        - Projects and roles
    - Automatic skill duration calculation based on work history
    - Multi-language support with automatic translation to English
    - Technical skill aggregation across experiences

    The processor performs:
    - Document text extraction
    - Structured data parsing
    - Experience duration calculations
    - Skill categorization and aggregation
    - Role suitability analysis

    Attributes:
        openai_client (OpenAIClient): Client for OpenAI API interactions
        langchain_client (LangChainClient): Client for LangChain operations
    """
    def __init__(self, openai_client: OpenAIClient, langchain_client: LangChainClient):
        """
        Initializes the CVProcessor with the required OpenAI and
        LangChain clients.
        """
        self.openai_client = openai_client
        self.langchain_client = langchain_client

    async def process(self, file: Optional[UploadFile], data: Optional[str] = None) -> dict:
        """
        Main entry point to process a CV. Either a file or a URL must be
        provided.

        :param file: The uploaded file to be processed (PDF or DOCX).
        :param data: A URL from which the file can be downloaded, if no
                     file is provided.
        :return: Dictionary containing the extracted CV data.
        """
        if not file and not data:
            raise HTTPException(status_code=400, detail="The CV action requires a file or URL")
        result = await self.process_cv(file, data)
        return result

    async def process_cv(self, file: UploadFile, data: Optional[str] = None) -> dict:
        """
        Processor for extracting structured information from CV/resume documents.

        Supports multiple file formats including PDF and DOCX files. Processes CVs to extract
        structured information about candidates' professional profiles using language models.

        Key Features:
        - Comprehensive information extraction including:
            - Personal and contact details
            - Professional summary
            - Education history
            - Work experience with technical skills used
            - Skills assessment with experience calculation
            - Certifications and languages
            - Projects and roles
        - Automatic skill duration calculation based on work history
        - Multi-language support with automatic translation to English
        - Technical skill aggregation across experiences

        The processor performs:
        - Document text extraction
        - Structured data parsing
        - Experience duration calculations
        - Skill categorization and aggregation
        - Role suitability analysis

        Attributes:
            openai_client (OpenAIClient): Client for OpenAI API interactions
            langchain_client (LangChainClient): Client for LangChain operations
        """
        # Use our TempFileTracker context manager for better cleanup
        with tempfile.TemporaryDirectory() as temp_dir, TempFileTracker() as file_tracker:
            try:
                # ------------------------------------------------------------
                # 1. Download or read the file
                # ------------------------------------------------------------
                if data:
                    url = data.strip()
                    response = requests.get(url)
                    if response.status_code != 200:
                        raise HTTPException(status_code=400, detail="No se pudo descargar el archivo desde la URL proporcionada.")
                    filename = url.split("/")[-1].split("?")[0]
                    file_extension = filename.split(".")[-1].lower()

                    file_path = os.path.join(temp_dir, filename)
                    with open(file_path, "wb") as f:
                        f.write(response.content)
                    # Track the temporary file
                    file_tracker.add(file_path)
                else:
                    filename = file.filename
                    file_extension = filename.split(".")[-1].lower()

                    file_path = os.path.join(temp_dir, filename)
                    file_content = await file.read()
                    with open(file_path, "wb") as f:
                        f.write(file_content)
                    # Track the temporary file
                    file_tracker.add(file_path)

                # ------------------------------------------------------------
                # 2. Extract text from PDF or DOCX
                # ------------------------------------------------------------
                if file_extension == 'pdf':
                    # Process PDF as images for structured data extraction
                    print("Procesando CV como imágenes para extraer datos estructurados directamente.")

                    # Initialize accumulated CV data
                    accumulated_cv_data = None
                    accumulated_token_usage = {
                        "prompt_tokens": 0,
                        "completion_tokens": 0,
                        "total_tokens": 0,
                        "cost": 0.0
                    }

                    with fitz.open(file_path) as doc:
                        # Log the number of pages
                        page_count = len(doc)
                        print(f"Documento con {page_count} páginas detectado.")

                        for i, page in enumerate(doc):
                            # Render page to image with dpi=300
                            pix = page.get_pixmap(dpi=300)
                            image_filename = f"page-{page.number}.png"
                            image_path = os.path.join(temp_dir, image_filename)
                            pix.save(image_path)
                            file_tracker.add(image_path)  # Track for cleanup

                            # Encode the image to base64
                            base64_image_data = self.encode_image(image_path)

                            # Create a prompt that's appropriate for this page
                            if i == 0:
                                page_prompt = """Extract all structured information from this CV page.
                                This is the first or only page of the CV, so it likely contains personal information,
                                summary, and possibly the start of education or work experience sections.

                                CRITICAL: For each work experience, extract ALL responsibilities listed. Do not omit any responsibilities
                                or summarize them - include each bullet point as a separate item in the responsibilities list. If there
                                are multiple bullet points with responsibilities, make sure to capture every single one. The responsibilities field
                                should NEVER be null if there are bullet points in the text.

                                When you see "Key responsibilities:" followed by bullet points, these are responsibilities for the most recently
                                mentioned job title and company. Make sure to extract ALL of these bullet points into the responsibilities field.

                                IMPORTANT: If a work experience entry appears to be cut off at the end of the page (for example, if the last bullet point
                                is incomplete or if there are clearly more responsibilities that continue onto the next page), still extract all the
                                information available on this page. The system will merge the information from subsequent pages."""
                            else:
                                page_prompt = f"""Extract all structured information from this CV page.
                                This is page {i+1} of the CV, which likely continues from previous pages.

                                IMPORTANT: If you see information that appears to be a continuation of a work experience
                                entry from a previous page (such as responsibilities, client details, or project information
                                for a job title already mentioned), make sure to include the job title and company name
                                exactly as they appeared on the previous page, along with the new information.

                                For example, if page {i} had a work experience entry for "Senior Consultant" at "Capco Technologies"
                                and this page contains additional details like client information or responsibilities for that same role,
                                include both the job title "Senior Consultant", company name "Capco Technologies", AND the new information.

                                CRITICAL: For each work experience, extract ALL responsibilities listed. Do not omit any responsibilities
                                or summarize them - include each bullet point as a separate item in the responsibilities list. If there
                                are multiple bullet points with responsibilities, make sure to capture every single one. The responsibilities field
                                should NEVER be null if there are bullet points in the text.

                                VERY IMPORTANT: If this page starts with client information (like "Client: JP Morgan, EMEA"), project scope,
                                or bullet points that appear to be a continuation from a previous page's work experience, treat it as a continuation
                                of the last work experience entry from the previous page. Include the job title and company name from the previous page,
                                and add these details to that work experience.

                                For example, if this page starts with "Client: JP Morgan, EMEA" or "Project Scope: Letter of Credit application..."
                                and doesn't mention a job title, it's likely a continuation of the last work experience from the previous page.
                                In this case, you should include the job title "Senior Consultant" and company name "Capco Technologies" from
                                the previous page, along with the client and project information from this page.

                                EXTREMELY IMPORTANT: If you see bullet points at the beginning of the page without a clear job title or dates, these are almost
                                certainly responsibilities that belong to the last work experience from the previous page. In this case, make sure to:
                                1. Include the job title and company name from the previous page
                                2. Add ALL these bullet points to the responsibilities list for that job
                                3. Do NOT create a new work experience entry for these bullet points
                                4. The responsibilities field should NEVER be empty or null when bullet points are present

                                CRITICAL FOR PAGE TRANSITIONS: If you see "Key responsibilities:" at the beginning of the page followed by bullet points,
                                these are DEFINITELY responsibilities for the job mentioned at the end of the previous page. You MUST:
                                1. Include the exact job title and company name from the previous page
                                2. Extract EACH bullet point as a separate responsibility
                                3. Do NOT treat "Key responsibilities:" as a new job title or section header
                                4. Make sure the responsibilities field contains ALL bullet points that follow "Key responsibilities:"

                                IMPORTANT: If you see a new job title with start and end dates (like "Designation: Senior Business Analyst" followed by
                                "Organization: Wipro Technology" with dates), this is a NEW job entry, not a continuation of the previous one. Create a
                                separate work experience entry for this job with its own responsibilities.

                                When you see "Key responsibilities:" followed by bullet points, these are responsibilities for the most recently
                                mentioned job title and company. Make sure to extract ALL of these bullet points into the responsibilities field.

                                CRITICAL FOR WORK EXPERIENCE SPANNING PAGES: If you see text like "o Data analysis – expertise in writing medium to complex SQL queries"
                                at the beginning of the page without a job title, this is likely a continuation of responsibilities from the previous page.
                                In this case, include the job title and company name from the previous page, and add these responsibilities to that job.

                                IMPORTANT FOR MULTI-PAGE RESPONSIBILITIES: If a responsibility appears to be a continuation from the previous page
                                (for example, if it starts with lowercase letters or continues a thought), still extract it as a complete responsibility.
                                The system will handle merging and deduplication of responsibilities.

                                SPECIAL CASE - PROJECT SCOPE: If you see "Project Scope:" followed by text, this is NOT a responsibility itself.
                                Instead, look for "Key responsibilities:" that follows it, which will contain the actual responsibilities as bullet points.
                                Make sure to extract each bullet point after "Key responsibilities:" as a separate responsibility.

                                Focus on any new information not captured from previous pages, such as additional
                                work experiences, skills, certifications, or other sections."""


                            # Process the page with get_structured_data_image
                            # If we have accumulated data, pass it as initial_data
                            try:
                                # Print the prompt being sent
                                print("\n---------------------------------------------------------------------")
                                print(f"A: {page_prompt}")

                                # If we have accumulated data, print it too
                                if accumulated_cv_data:
                                    print(f"\nI already have some information extracted: {json.dumps(accumulated_cv_data, indent=2, ensure_ascii=False)}\n")
                                    print("Please fill in any missing fields or correct any errors based on the image, while preserving the existing data.")

                                # Call the API
                                page_result = await self.langchain_client.get_structured_data_image(
                                    CV,  # The Pydantic model
                                    base64_image_data,
                                    page_prompt,
                                    initial_data=accumulated_cv_data  # Pass accumulated data for subsequent pages
                                )

                                # Print the result
                                print(f"R: {json.dumps(page_result['response'], indent=2, ensure_ascii=False)}")
                                print("---------------------------------------------------------------------")

                                # Accumulate token usage data
                                if 'token_usage' in page_result:
                                    token_usage = page_result['token_usage']
                                    accumulated_token_usage["prompt_tokens"] += token_usage.get("prompt_tokens", 0)
                                    accumulated_token_usage["completion_tokens"] += token_usage.get("completion_tokens", 0)
                                    accumulated_token_usage["total_tokens"] += token_usage.get("total_tokens", 0)
                                    accumulated_token_usage["cost"] += token_usage.get("cost", 0.0)

                                # Update accumulated data with the result from this page
                                if accumulated_cv_data is None:
                                    # First page, just use the result
                                    accumulated_cv_data = page_result["response"]
                                    print(f"Datos extraídos de la página {i+1} (primera página)")
                                else:
                                    # Merge the new data with accumulated data
                                    new_data = page_result["response"]
                                    print(f"\nDatos acumulados antes de la fusión: {json.dumps(accumulated_cv_data, indent=2, ensure_ascii=False)}")
                                    print(f"\nNuevos datos de la página {i+1}: {json.dumps(new_data, indent=2, ensure_ascii=False)}")

                                    accumulated_cv_data = merge_cv_data(accumulated_cv_data, new_data)

                                    print(f"\nDatos fusionados: {json.dumps(accumulated_cv_data, indent=2, ensure_ascii=False)}")
                                    print(f"Actualizando datos con información de la página {i+1}")

                                # Clean up the image file
                                file_tracker.remove(image_path)

                            except Exception as e:
                                print(f"Error procesando la página {i+1}: {str(e)}")
                                # Continue with next page even if this one fails

                    # Clean up the original file
                    file_tracker.remove(file_path)

                    # If we successfully extracted structured data, return it directly
                    if accumulated_cv_data:
                        # Add token usage to CV data temporarily
                        if accumulated_token_usage["total_tokens"] > 0:
                            accumulated_cv_data["token_usage"] = {
                                "prompt_tokens": accumulated_token_usage["prompt_tokens"],
                                "completion_tokens": accumulated_token_usage["completion_tokens"],
                                "total_tokens": accumulated_token_usage["total_tokens"],
                                "cost": accumulated_token_usage["cost"]
                            }

                        # Convert token_usage to extraction_cost (following invoice processor pattern)
                        if 'token_usage' in accumulated_cv_data:
                            accumulated_cv_data['extraction_cost'] = {
                                'tokens': {
                                    'prompt_tokens': accumulated_cv_data['token_usage']['prompt_tokens'],
                                    'completion_tokens': accumulated_cv_data['token_usage']['completion_tokens'],
                                    'total_tokens': accumulated_cv_data['token_usage']['total_tokens']
                                },
                                'cost_usd': accumulated_cv_data['token_usage']['cost']
                            }
                            # Remove the internal token_usage field
                            del accumulated_cv_data['token_usage']

                        print("\n=====================================================================")
                        print("RESULTADO FINAL DESPUÉS DE PROCESAR TODAS LAS PÁGINAS:")
                        print(json.dumps(accumulated_cv_data, indent=2, ensure_ascii=False))
                        print("=====================================================================\n")
                        return {"response": accumulated_cv_data}

                    # If structured extraction failed, fall back to text extraction
                    print("La extracción estructurada falló, volviendo al método basado en texto.")
                    # We need to reopen the file since we removed it, so we'll use a fallback approach
                    CV_tot_text = await get_text_from_pdf(file_path) if os.path.exists(file_path) else ""

                elif file_extension == 'docx':
                    # For DOCX files, process page by page similar to PDFs
                    try:
                        # Extract text page by page
                        print("Procesando DOCX página por página para extraer datos estructurados.")

                        # Initialize accumulated CV data
                        accumulated_cv_data = None
                        accumulated_token_usage = {
                            "prompt_tokens": 0,
                            "completion_tokens": 0,
                            "total_tokens": 0,
                            "cost": 0.0
                        }

                        # Get text from each page
                        pages_text = await get_text_from_word_by_pages(file_path)
                        page_count = len(pages_text)
                        print(f"Documento DOCX con {page_count} páginas estimadas detectado.")

                        # Process each page
                        for i, page_text in enumerate(pages_text):
                            # Create a prompt that's appropriate for this page
                            if i == 0:
                                page_prompt = """Extract all structured information from this CV page.
                                This is the first or only page of the CV, so it likely contains personal information,
                                summary, and possibly the start of education or work experience sections.

                                CRITICAL: For each work experience, extract ALL responsibilities listed. Do not omit any responsibilities
                                or summarize them - include each bullet point as a separate item in the responsibilities list. If there
                                are multiple bullet points with responsibilities, make sure to capture every single one. The responsibilities field
                                should NEVER be null if there are bullet points in the text.

                                When you see "Key responsibilities:" followed by bullet points, these are responsibilities for the most recently
                                mentioned job title and company. Make sure to extract ALL of these bullet points into the responsibilities field.

                                IMPORTANT: If a work experience entry appears to be cut off at the end of the page (for example, if the last bullet point
                                is incomplete or if there are clearly more responsibilities that continue onto the next page), still extract all the
                                information available on this page. The system will merge the information from subsequent pages."""
                            else:
                                page_prompt = f"""Extract all structured information from this CV page.
                                This is page {i+1} of the CV, which likely continues from previous pages.

                                IMPORTANT: If you see information that appears to be a continuation of a work experience
                                entry from a previous page (such as responsibilities, client details, or project information
                                for a job title already mentioned), make sure to include the job title and company name
                                exactly as they appeared on the previous page, along with the new information.

                                For example, if page {i} had a work experience entry for "Senior Consultant" at "Capco Technologies"
                                and this page contains additional details like client information or responsibilities for that same role,
                                include both the job title "Senior Consultant", company name "Capco Technologies", AND the new information.

                                CRITICAL: For each work experience, extract ALL responsibilities listed. Do not omit any responsibilities
                                or summarize them - include each bullet point as a separate item in the responsibilities list. If there
                                are multiple bullet points with responsibilities, make sure to capture every single one. The responsibilities field
                                should NEVER be null if there are bullet points in the text.

                                VERY IMPORTANT: If this page starts with client information (like "Client: JP Morgan, EMEA"), project scope,
                                or bullet points that appear to be a continuation from a previous page's work experience, treat it as a continuation
                                of the last work experience entry from the previous page. Include the job title and company name from the previous page,
                                and add these details to that work experience.

                                For example, if this page starts with "Client: JP Morgan, EMEA" or "Project Scope: Letter of Credit application..."
                                and doesn't mention a job title, it's likely a continuation of the last work experience from the previous page.
                                In this case, you should include the job title "Senior Consultant" and company name "Capco Technologies" from
                                the previous page, along with the client and project information from this page.

                                EXTREMELY IMPORTANT: If you see bullet points at the beginning of the page without a clear job title or dates, these are almost
                                certainly responsibilities that belong to the last work experience from the previous page. In this case, make sure to:
                                1. Include the job title and company name from the previous page
                                2. Add ALL these bullet points to the responsibilities list for that job
                                3. Do NOT create a new work experience entry for these bullet points
                                4. The responsibilities field should NEVER be empty or null when bullet points are present

                                CRITICAL FOR PAGE TRANSITIONS: If you see "Key responsibilities:" at the beginning of the page followed by bullet points,
                                these are DEFINITELY responsibilities for the job mentioned at the end of the previous page. You MUST:
                                1. Include the exact job title and company name from the previous page
                                2. Extract EACH bullet point as a separate responsibility
                                3. Do NOT treat "Key responsibilities:" as a new job title or section header
                                4. Make sure the responsibilities field contains ALL bullet points that follow "Key responsibilities:"

                                IMPORTANT: If you see a new job title with start and end dates (like "Designation: Senior Business Analyst" followed by
                                "Organization: Wipro Technology" with dates), this is a NEW job entry, not a continuation of the previous one. Create a
                                separate work experience entry for this job with its own responsibilities.

                                When you see "Key responsibilities:" followed by bullet points, these are responsibilities for the most recently
                                mentioned job title and company. Make sure to extract ALL of these bullet points into the responsibilities field.

                                CRITICAL FOR WORK EXPERIENCE SPANNING PAGES: If you see text like "o Data analysis – expertise in writing medium to complex SQL queries"
                                at the beginning of the page without a job title, this is likely a continuation of responsibilities from the previous page.
                                In this case, include the job title and company name from the previous page, and add these responsibilities to that job.

                                IMPORTANT FOR MULTI-PAGE RESPONSIBILITIES: If a responsibility appears to be a continuation from the previous page
                                (for example, if it starts with lowercase letters or continues a thought), still extract it as a complete responsibility.
                                The system will handle merging and deduplication of responsibilities.

                                SPECIAL CASE - PROJECT SCOPE: If you see "Project Scope:" followed by text, this is NOT a responsibility itself.
                                Instead, look for "Key responsibilities:" that follows it, which will contain the actual responsibilities as bullet points.
                                Make sure to extract each bullet point after "Key responsibilities:" as a separate responsibility.

                                Focus on any new information not captured from previous pages, such as additional
                                work experiences, skills, certifications, or other sections."""

                            # Process the page with get_structured_data_image (despite not being an image)
                            # If we have accumulated data, pass it as initial_data
                            try:
                                # Print the prompt being sent
                                print("\n---------------------------------------------------------------------")
                                print(f"A: {page_prompt}")

                                # If we have accumulated data, print it too
                                if accumulated_cv_data:
                                    print(f"\nI already have some information extracted: {json.dumps(accumulated_cv_data, indent=2, ensure_ascii=False)}\n")
                                    print("Please fill in any missing fields or correct any errors based on the text, while preserving the existing data.")

                                # Prepare the data for the API call
                                data = f"Here is the CV text from page {i+1}:\n\n{page_text}"

                                # Call the API using get_structured_data_test instead of get_structured_data
                                page_result = await self.langchain_client.get_structured_data_test(
                                    CV,  # The Pydantic model
                                    data,
                                    page_prompt,
                                    initial_data=accumulated_cv_data  # Pass accumulated data for subsequent pages
                                )

                                # Print the result
                                print(f"R: {json.dumps(page_result['response'], indent=2, ensure_ascii=False)}")
                                print("---------------------------------------------------------------------")

                                # Accumulate token usage data
                                if 'token_usage' in page_result:
                                    token_usage = page_result['token_usage']
                                    accumulated_token_usage["prompt_tokens"] += token_usage.get("prompt_tokens", 0)
                                    accumulated_token_usage["completion_tokens"] += token_usage.get("completion_tokens", 0)
                                    accumulated_token_usage["total_tokens"] += token_usage.get("total_tokens", 0)
                                    accumulated_token_usage["cost"] += token_usage.get("cost", 0.0)

                                # Update accumulated data with the result from this page
                                if accumulated_cv_data is None:
                                    # First page, just use the result
                                    accumulated_cv_data = page_result["response"]
                                    print(f"Datos extraídos de la página {i+1} (primera página)")
                                else:
                                    # Merge the new data with accumulated data
                                    new_data = page_result["response"]
                                    print(f"\nDatos acumulados antes de la fusión: {json.dumps(accumulated_cv_data, indent=2, ensure_ascii=False)}")
                                    print(f"\nNuevos datos de la página {i+1}: {json.dumps(new_data, indent=2, ensure_ascii=False)}")

                                    accumulated_cv_data = merge_cv_data(accumulated_cv_data, new_data)

                                    print(f"\nDatos fusionados: {json.dumps(accumulated_cv_data, indent=2, ensure_ascii=False)}")
                                    print(f"Actualizando datos con información de la página {i+1}")

                            except Exception as e:
                                print(f"Error procesando la página {i+1}: {str(e)}")
                                # Continue with next page even if this one fails

                        # Clean up the file
                        file_tracker.remove(file_path)

                        # If we successfully extracted structured data, return it directly
                        if accumulated_cv_data:
                            # Add token usage to CV data temporarily
                            if accumulated_token_usage["total_tokens"] > 0:
                                accumulated_cv_data["token_usage"] = {
                                    "prompt_tokens": accumulated_token_usage["prompt_tokens"],
                                    "completion_tokens": accumulated_token_usage["completion_tokens"],
                                    "total_tokens": accumulated_token_usage["total_tokens"],
                                    "cost": accumulated_token_usage["cost"]
                                }

                            # Convert token_usage to extraction_cost (following invoice processor pattern)
                            if 'token_usage' in accumulated_cv_data:
                                accumulated_cv_data['extraction_cost'] = {
                                    'tokens': {
                                        'prompt_tokens': accumulated_cv_data['token_usage']['prompt_tokens'],
                                        'completion_tokens': accumulated_cv_data['token_usage']['completion_tokens'],
                                        'total_tokens': accumulated_cv_data['token_usage']['total_tokens']
                                    },
                                    'cost_usd': accumulated_cv_data['token_usage']['cost']
                                }
                                # Remove the internal token_usage field
                                del accumulated_cv_data['token_usage']

                            print("\n=====================================================================")
                            print("RESULTADO FINAL DESPUÉS DE PROCESAR TODAS LAS PÁGINAS:")
                            print(json.dumps(accumulated_cv_data, indent=2, ensure_ascii=False))
                            print("=====================================================================\n")
                            return {"response": accumulated_cv_data}

                        # If structured extraction failed, fall back to text extraction
                        print("La extracción estructurada falló, volviendo al método basado en texto completo.")
                        CV_tot_text = await get_text_from_word(file_path) if os.path.exists(file_path) else ""

                    except Exception as e:
                        print(f"Error procesando DOCX página por página: {str(e)}")
                        # Fall back to full text extraction
                        try:
                            CV_tot_text = await get_text_from_word(file_path) if os.path.exists(file_path) else ""
                        except Exception as e2:
                            print(f"Error extracting text from DOCX: {str(e2)}")
                            CV_tot_text = ""

                else:
                    raise HTTPException(status_code=400, detail="El archivo debe ser un PDF o Docx.")

                # ------------------------------------------------------------
                # 3. Process the extracted text to get structured data
                # ------------------------------------------------------------
                CV_data = await self.get_structured_data(CV_tot_text, CV)

                return CV_data

            except asyncio.CancelledError:
                # The TempFileTracker will automatically clean up files in its __exit__ method
                # We just need to re-raise to propagate the cancellation
                raise

            except Exception as e:
                raise HTTPException(status_code=500, detail=f"Error al procesar el CV: {str(e)}")

    async def get_structured_data(self, CV_text: str, CVModel: type[BaseModel]):
        """
        Extract structured information from CV text using language model.

        Args:
            CV_text (str): Processed CV text
            CVModel (type[BaseModel]): Pydantic model to structure the data

        Returns:
            dict: Structured CV data

        Raises:
            HTTPException: Passed through from LangChainClient
        """
        data = f"Here is the CV text: {CV_text}"

        # Call the langchain_client's get_structured_data method
        # The helper function handles all validation, retries, and error cases
        response = await self.langchain_client.get_structured_data(CVModel, data, prompt)

        # Add extraction cost information to the CV data if available (following invoice processor pattern)
        if 'token_usage' in response and 'response' in response:
            token_usage = response['token_usage']
            if token_usage.get("total_tokens", 0) > 0:
                # Add token_usage to CV data temporarily
                response['response']["token_usage"] = {
                    "prompt_tokens": token_usage.get("prompt_tokens", 0),
                    "completion_tokens": token_usage.get("completion_tokens", 0),
                    "total_tokens": token_usage.get("total_tokens", 0),
                    "cost": token_usage.get("cost", 0.0)
                }

                # Convert token_usage to extraction_cost
                response['response']['extraction_cost'] = {
                    'tokens': {
                        'prompt_tokens': response['response']['token_usage']['prompt_tokens'],
                        'completion_tokens': response['response']['token_usage']['completion_tokens'],
                        'total_tokens': response['response']['token_usage']['total_tokens']
                    },
                    'cost_usd': response['response']['token_usage']['cost']
                }

                # Remove the internal token_usage field
                del response['response']['token_usage']

        # Return the response
        return response


    def encode_image(self, image_path: str) -> str:
        """
        Processor for extracting structured information from CV/resume documents.

        Supports multiple file formats including PDF and DOCX files. Processes CVs to extract
        structured information about candidates' professional profiles using language models.

        Key Features:
        - Comprehensive information extraction including:
            - Personal and contact details
            - Professional summary
            - Education history
            - Work experience with technical skills used
            - Skills assessment with experience calculation
            - Certifications and languages
            - Projects and roles
        - Automatic skill duration calculation based on work history
        - Multi-language support with automatic translation to English
        - Technical skill aggregation across experiences

        The processor performs:
        - Document text extraction
        - Structured data parsing
        - Experience duration calculations
        - Skill categorization and aggregation
        - Role suitability analysis

        Attributes:
            openai_client (OpenAIClient): Client for OpenAI API interactions
            langchain_client (LangChainClient): Client for LangChain operations
        """
        extension = image_path.split('.')[-1].lower()
        mime_types = {
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg'
        }
        mime_type = mime_types.get(extension, 'application/octet-stream')
        with open(image_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')
        return f"data:{mime_type};base64,{base64_image}"

